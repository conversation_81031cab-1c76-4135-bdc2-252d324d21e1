<template>
  <div class="container-fluid">
    <div class="top-box">
      <el-row :gutter="53" style="height: 100%;">
        <el-col :span="16" style="height: 100%;">
          <div class="grid-content top-left" >
             <div class="grid-item" v-for="item in numberData" :key="item">
              <div class="grid-item-title">{{ item.title }}</div>
              <div class="grid-item-content">
                <div class="content-left iconfont">
                  <img :src="item.onLine" alt="">
                  <div class="iconfont-value">{{ item.value }}</div>
                  <div class="iconfont-title">在线</div>
                </div>
                <div class="content-right iconfont">
                  <img :src="item.offline" alt="">
                  <div class="iconfont-values">{{ item.values }}</div>
                  <div class="iconfont-title">离线</div>
                </div>
              </div>
             </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="grid-content top-right" >
            <div class="title-box-style">楼宇控制</div>
            <div class="top-right-content">
              <div class="right-content-item">
                <img src="/public/index-icon/qd-icon.png" alt="">
                <div class="right-content-item-text">
                  <div>32</div>
                  <div>启动</div>
                </div>
              </div>
              <div class="right-content-item">
                <img src="/public/index-icon/tz-icon.png" alt="">
                <div class="right-content-item-text">
                  <div>32</div>
                  <div>停止</div>
                </div>
              </div>
              <div class="right-content-item">
                <img src="/public/index-icon/gz-icon.png" alt="">
                <div class="right-content-item-text">
                  <div>32</div>
                  <div>故障</div>
                </div>
              </div>
              <div class="right-content-item">
                <img src="/public/index-icon/baojin-icon.png" alt="">
                <div class="right-content-item-text">
                  <div>32</div>
                  <div>报警</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="content-bottom">
      <el-row :gutter="20" style="height: 100%;">
        <el-col :span="14" >
          <div class="grid-content bottom-left" >
            <div class="chart-box">
              <div class="top">
                <div class="title-box-style">能耗趋势</div>
              </div>
              <Energyconsumption></Energyconsumption>
            </div>
            <div class="chart-box">
              <div class="top">
                <div class="title-box-style">楼宇控制设备运行趋势</div>
              </div>
              <Equipmentoperation></Equipmentoperation>
            </div>
          </div>
        </el-col>
        <el-col :span="10">
          <div class="grid-content bottom-right" >
            <div class="title-box-style" style="padding: 11px 0 0 16px;">报警记录</div>
            <div class="table-box">
              <el-table :data="tableData" style="width: 100%" :header-cell-style="headerCellStyle" :row-style="rowStyle" v-loading="loading">
                <el-table-column prop="deviceName" label="设备名称" />
                <el-table-column prop="deviceNo" label="设备编号" />
                <el-table-column prop="alarmType" label="设备类型" />
                <el-table-column prop="alarmTime" label="报警时间" />
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="scope">
                    <span :class="scope.row.status === '已处理' ? 'status-handled' : 'status-unhandled'">
                      {{ scope.row.status }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="result" label="处理结果"/>
                <!-- <el-table-column prop="action" label="操作" width="80">
                  <template #default="scope">
                    <el-button type="text" class="action-btn">查看详情</el-button>
                  </template>
                </el-table-column> -->
              </el-table>
            </div>
            <div class="table-footer">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="pagination.total"
                :current-page="pagination.current"
                :page-size="pagination.size"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import Energyconsumption from "@/components/wel/energyconsumption.vue"
import Equipmentoperation from "@/components/wel/equipmentoperation.vue"
import { getAlarmList, getEnergyTrend, getAllDeviceOnlineStatus,  } from "@/api/kaihua/wel.js"
// 表格数据
const tableData = ref([])
// 加载状态
const loading = ref(false)
// 分页数据
const pagination = ref({
  current: 1,
  size: 10,
  total: 0
})

// 获取报警记录列表
const fetchAlarmList = async () => {
  loading.value = true
  try {
    const response = await getAlarmList(pagination.value.current, pagination.value.size)
    if (response.data && response.data.code === 200) {
      const data = response.data.data
      tableData.value = data.records || []
      pagination.value.total = data.total || 0
    } else {
      ElMessage.error('获取报警记录失败：' + (response.data?.message || '未知错误'))
      tableData.value = []
    }
  } catch (error) {
    console.error('获取报警记录失败:', error)
    ElMessage.error('获取报警记录失败：' + error.message)
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handleCurrentChange = (currentPage) => {
  pagination.value.current = currentPage
  fetchAlarmList()
}

// 处理页面大小变化
const handleSizeChange = (pageSize) => {
  pagination.value.size = pageSize
  pagination.value.current = 1
  fetchAlarmList()
}

// 表格头部样式
const headerCellStyle = {
  backgroundColor: '#F5F7FA',
  color: '#606266',
  fontWeight: '500',
  fontSize: '14px',
  height: '40px',
  borderBottom: '1px solid #EBEEF5'
}

// 表格行样式
const rowStyle = ({ rowIndex }) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? '#FFFFFF' : '#FAFAFA',
    height: '40px'
  }
}

const numberData = ref(
  [
    { 
      title: '出入口设备',
      value: 100,
      values:2,
      onLine: '/index-icon/crk-icon.png',
      offline: '/index-icon/crks-icon.png',
    },
    { 
      title: '视频监控设备',
      value: 200,
      values:22,
      onLine: '/index-icon/jk-icon.png',
      offline: '/index-icon/jks-icon.png',
    },
    { 
      title: '报警设备',
      value: 200,
      values:21,
      onLine: '/index-icon/bj-icon.png',
      offline: '/index-icon/bjs-icon.png',
    }, 
    { 
      title: '车场道闸设备',
      value: 200,
      values:21,
      onLine: '/index-icon/ccdz-icon.png',
      offline: '/index-icon/ccdzs-icon.png',
    }, 
    { 
      title: '水表',
      value: 200,
      values:21,
      onLine: '/index-icon/sb-icon.png',
      offline: '/index-icon/sbs-icon.png',
    }, 
    { 
      title: '电表',
      value: 200,
      values:21,
      onLine: '/index-icon/db-icon.png',
      offline: '/index-icon/dbs-icon.png',
    },
    { 
      title: '空调',
      value: 200,
      values:21,
      onLine: '/index-icon/kt-icon.png',
      offline: '/index-icon/kts-icon.png',
    },
    { 
      title: '水泵',
      value: 200,
      values:21,
      onLine: '/index-icon/suiben-icon.png',
      offline: '/index-icon/suibens-icon.png',
    },
  ]
)

// 组件挂载时获取数据
onMounted(() => {
  fetchAlarmList()
})

</script>

<style scoped lang="scss">
*{
  box-sizing: border-box;
}
.container-fluid {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: #f1f3f5;
}
.top-box{
  width: 100%;
  height: 374px;
  border-radius: 8px;
  background: #FFFFFF;
  padding: 16px;
  margin-bottom: 17px;
}
.grid-content {
  border-radius: 4px;
  height: 100%;
  // background-color: pink;
}
.top-left{
  display: grid;
  // 一行四个，一共两行
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 160px);
  grid-gap: 22px 53px;
}
.top-right{
  display: flex;
  flex-direction: column;
  padding: 31px 16px 0 16px;
  background: linear-gradient(180deg, rgba(174, 201, 254, 0.35) 0%, rgba(210, 224, 253, 0.14) 147%);
}
.grid-item{
  padding: 12px 16px;
  background: #F7F8FA;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
}
.grid-item-title{
  font-family: Source Han Sans;
  font-size: 20px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #333333;
  margin-bottom: 20px;
}
.grid-item-content{
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 84px);
  grid-gap: 29px;
  .content-left{
    border-radius: 8px;
    background: linear-gradient(143deg, #72FFB6 -265%, #FEFFFF 75%);
    box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.5);
  }
  .content-right{
    border-radius: 8px;
    background: linear-gradient(140deg, #FEA7A7 -262%, #FEFFFF 84%);
    box-shadow: 0px 4px 4px 0px rgba(217, 217, 217, 0.5);
  }
  .iconfont{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img{
      width: 24px;
      height: 24px;
    }
    .iconfont-title{ 
      font-family: Source Han Sans;
      font-size: 12px;
      font-weight: 500;
      color: #333333;
    }
    .iconfont-value{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: bold;
      color: #4CAF50;
    }
    .iconfont-values{
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: bold;
      color: #FF4444;
    }
  }
}

.content-bottom{
  width: 100%;
  height: calc(100% - 380px);
}
.bottom-left{
  display: grid;
  // 两行一个显示两行
  grid-template-columns: repeat(1, 1fr);
  grid-template-rows: repeat(2, 185px);
  grid-gap: 14px;
  .chart-box{
    position: relative;
    padding: 11px 0 0 16px;
    border-radius: 8px;
    background: #FFFFFF;
  }
}

.bottom-right{
  background: #FFFFFF;
}

.title-box-style{
  font-family: Source Han Sans;
  font-size: 16px;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0em;
  font-variation-settings: "opsz" auto;
  font-feature-settings: "kern" on;
  color: #333333;
  display: flex;
  align-items: center;
  &::before{
    content: '';
    display: block;
    width: 4px;
    height: 21px;
    border-radius: 50px;
    background: linear-gradient(180deg, #02A7F0 0%, rgba(0, 177, 255, 0) 129%);
    margin-right: 12px;
  }
}
.top-right-content{
  flex: 1;
  margin: 39px 0 47px 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 23px 32px;
}
.right-content-item{
  border-radius: 8px;
  background: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  img{
    width: 36px;
    height: 36px;
    margin-right: 12px;
  }
  .right-content-item-text{
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-family: Source Han Sans;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0em;
    font-variation-settings: "opsz" auto;
    font-feature-settings: "kern" on;
    color: #3D3D3D;
    text-align: center;
  }
}

// 分页器
.table-footer{
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding: 0 16px;
}

// 表格样式
.table-box {
  padding: 16px;
  height: calc(100% - 80px);

  :deep(.el-table) {
    border: none;
    font-size: 14px;

    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #F5F7FA !important;
          border-bottom: 1px solid #EBEEF5;
          padding: 8px 0;

          .cell {
            color: #606266;
            font-weight: 500;
            font-size: 14px;
          }
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:nth-child(odd) {
            background-color: #FFFFFF;
          }
          &:nth-child(even) {
            background-color: #FAFAFA;
          }

          td {
            border-bottom: 1px solid #EBEEF5;
            padding: 8px 0;

            .cell {
              color: #303133;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

.status-handled {
  color: #67C23A;
  font-weight: 500;
}

.status-unhandled {
  color: #F56C6C;
  font-weight: 500;
}

.action-btn {
  color: #409EFF;
  font-size: 14px;
  padding: 0;

  &:hover {
    color: #66B1FF;
  }
}
</style> 
