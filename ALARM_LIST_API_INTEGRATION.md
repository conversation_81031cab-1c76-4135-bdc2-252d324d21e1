# 报警记录表 API 集成更新

## 📋 更新概述

将 `src/views/wel/index.vue` 中的硬编码报警记录表数据替换为从 `getAlarmList` API 接口获取的动态数据。

## 🔧 主要修改

### 1. 导入必要的依赖
```javascript
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getAlarmList } from "@/api/kaihua/wel.js"
```

### 2. 替换静态数据为动态状态管理
```javascript
// 原来的硬编码数据
const tableData = ref([
  {
    deviceName: '1号',
    alarmTime: '2025/7/28',
    status: '已处理',
    result: '',
    action: '查看详情'
  },
  // ...
])

// 修改后的动态数据管理
const tableData = ref([])
const loading = ref(false)
const pagination = ref({
  current: 1,
  size: 10,
  total: 0
})
```

### 3. 添加 API 调用函数
```javascript
const fetchAlarmList = async () => {
  loading.value = true
  try {
    const response = await getAlarmList(pagination.value.current, pagination.value.size)
    if (response.data && response.data.code === 200) {
      const data = response.data.data
      tableData.value = data.records || []
      pagination.value.total = data.total || 0
    } else {
      ElMessage.error('获取报警记录失败：' + (response.data?.message || '未知错误'))
      tableData.value = []
    }
  } catch (error) {
    console.error('获取报警记录失败:', error)
    ElMessage.error('获取报警记录失败：' + error.message)
    tableData.value = []
  } finally {
    loading.value = false
  }
}
```

### 4. 添加分页处理函数
```javascript
const handleCurrentChange = (currentPage) => {
  pagination.value.current = currentPage
  fetchAlarmList()
}

const handleSizeChange = (pageSize) => {
  pagination.value.size = pageSize
  pagination.value.current = 1
  fetchAlarmList()
}
```

### 5. 添加生命周期钩子
```javascript
onMounted(() => {
  fetchAlarmList()
})
```

### 6. 更新模板
- 为表格添加加载状态：`v-loading="loading"`
- 更新分页组件配置：
```html
<el-pagination 
  background 
  layout="prev, pager, next" 
  :total="pagination.total"
  :current-page="pagination.current"
  :page-size="pagination.size"
  @current-change="handleCurrentChange"
  @size-change="handleSizeChange"
/>
```

## 📊 数据字段映射

表格列与 API 数据字段的对应关系：
- `deviceName` - 设备名称
- `deviceNo` - 设备编号  
- `alarmType` - 设备类型
- `alarmTime` - 报警时间
- `status` - 状态（已处理/未处理）
- `result` - 处理结果

## 🎯 功能特性

### 1. 动态数据加载
- 组件挂载时自动获取报警记录
- 支持分页数据加载
- 实时显示数据总数

### 2. 加载状态管理
- 表格加载状态指示器
- 防止重复请求

### 3. 错误处理
- API 调用失败时显示错误提示
- 网络错误的友好提示
- 错误日志记录

### 4. 分页功能
- 支持页码切换
- 支持页面大小调整
- 自动重新加载数据

### 5. 状态显示
- 动态状态样式（已处理/未处理）
- 响应式表格设计

## 🔄 API 接口

使用的 API 接口：`/bigScreen/alarmListPage`

### 请求参数
- `current`: 当前页码
- `size`: 每页数量
- `params`: 其他查询参数（可选）

### 响应格式
```javascript
{
  code: 200,
  data: {
    records: [...], // 报警记录数组
    total: 100      // 总记录数
  }
}
```

## ✅ 测试建议

1. **功能测试**
   - 验证页面加载时是否正确获取数据
   - 测试分页功能是否正常工作
   - 验证错误处理是否生效

2. **性能测试**
   - 检查加载状态是否正确显示
   - 验证数据更新的响应速度

3. **用户体验测试**
   - 确认错误提示信息友好
   - 验证表格样式和交互正常

## 🚀 部署注意事项

1. 确保后端 API 接口 `/bigScreen/alarmListPage` 可用
2. 验证 API 返回的数据格式与前端期望一致
3. 检查网络请求的权限配置
4. 确认分页参数的传递正确

## 📝 后续优化建议

1. 添加搜索和筛选功能
2. 支持报警记录的详情查看
3. 添加数据刷新功能
4. 考虑添加实时数据更新（WebSocket）
5. 优化移动端显示效果
