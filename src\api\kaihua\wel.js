import request from '@/axios';
export const getAlarmList = (current, size, params) => {
  return request({
    url: '/bigScreen/alarmListPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

// 获取每周能耗趋势
export const getWeekEnergyTrend = (params) => {
  return request({
    url: '/bigScreen/energyConsumptionTrendByWeek',
    method: 'get',
    params
  })
}

// 获取每日能耗趋势
export const getDayEnergyTrend = (params) => {
  return request({
    url: '/bigScreen/energyConsumptionTrendByDay',
    method: 'get',
    params
  })
}


// 获取所有设备在线状态
export const getAllDeviceOnlineStatus = () => {
  return request({
    url: '/bigScreen/deviceOnlineStatus',
    method: 'get',
  })
}